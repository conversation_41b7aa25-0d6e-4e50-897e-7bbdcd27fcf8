{"name": "astro", "version": "5.12.0", "description": "Astro is a modern site builder with web best practices, performance, and DX front-of-mind.", "type": "module", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/withastro/astro.git", "directory": "packages/astro"}, "bugs": "https://github.com/withastro/astro/issues", "homepage": "https://astro.build", "types": "./index.d.ts", "typesVersions": {"*": {"app": ["./dist/core/app/index"], "app/*": ["./dist/core/app/*"], "middleware": ["./dist/virtual-modules/middleware.d.ts"]}}, "exports": {".": {"types": "./index.d.ts", "default": "./dist/core/index.js"}, "./env": "./env.d.ts", "./env/runtime": "./dist/env/runtime.js", "./env/setup": "./dist/env/setup.js", "./types": "./types.d.ts", "./client": "./client.d.ts", "./astro-jsx": "./astro-jsx.d.ts", "./tsconfigs/*.json": "./tsconfigs/*", "./tsconfigs/*": "./tsconfigs/*.json", "./jsx/rehype.js": "./dist/jsx/rehype.js", "./jsx-runtime": {"types": "./jsx-runtime.d.ts", "default": "./dist/jsx-runtime/index.js"}, "./compiler-runtime": "./dist/runtime/compiler/index.js", "./runtime/*": "./dist/runtime/*", "./config": "./dist/config/entrypoint.js", "./container": "./dist/container/index.js", "./app": "./dist/core/app/index.js", "./app/node": "./dist/core/app/node.js", "./client/*": "./dist/runtime/client/*", "./components": "./components/index.ts", "./components/*": "./components/*", "./toolbar": "./dist/toolbar/index.js", "./actions/runtime/*": "./dist/actions/runtime/*", "./assets": "./dist/assets/index.js", "./assets/runtime": "./dist/assets/runtime.js", "./assets/utils": "./dist/assets/utils/index.js", "./assets/utils/inferRemoteSize.js": "./dist/assets/utils/remoteProbe.js", "./assets/endpoint/*": "./dist/assets/endpoint/*.js", "./assets/services/sharp": "./dist/assets/services/sharp.js", "./assets/services/noop": "./dist/assets/services/noop.js", "./assets/fonts/providers/*": "./dist/assets/fonts/providers/entrypoints/*.js", "./loaders": "./dist/content/loaders/index.js", "./content/config": "./dist/content/config.js", "./content/runtime": "./dist/content/runtime.js", "./content/runtime-assets": "./dist/content/runtime-assets.js", "./debug": "./components/Debug.astro", "./package.json": "./package.json", "./zod": {"types": "./zod.d.ts", "default": "./zod.mjs"}, "./errors": "./dist/core/errors/userError.js", "./middleware": "./dist/core/middleware/index.js", "./virtual-modules/*": "./dist/virtual-modules/*"}, "bin": {"astro": "astro.js"}, "files": ["components/*.{astro,css,ts}", "tsconfigs", "dist", "types", "astro.js", "index.d.ts", "zod.d.ts", "zod.mjs", "env.d.ts", "client.d.ts", "jsx-runtime.d.ts", "templates", "astro-jsx.d.ts", "types.d.ts", "README.md", "vendor"], "dependencies": {"@astrojs/compiler": "^2.12.2", "@capsizecss/unpack": "^2.4.0", "@oslojs/encoding": "^1.1.0", "@rollup/pluginutils": "^5.1.4", "acorn": "^8.14.1", "aria-query": "^5.3.2", "axobject-query": "^4.1.0", "boxen": "8.0.1", "ci-info": "^4.2.0", "clsx": "^2.1.1", "common-ancestor-path": "^1.0.1", "cookie": "^1.0.2", "cssesc": "^3.0.0", "debug": "^4.4.0", "deterministic-object-hash": "^2.0.2", "devalue": "^5.1.1", "diff": "^5.2.0", "dlv": "^1.1.3", "dset": "^3.1.4", "es-module-lexer": "^1.6.0", "esbuild": "^0.25.0", "estree-walker": "^3.0.3", "flattie": "^1.1.1", "fontace": "~0.3.0", "github-slugger": "^2.0.0", "html-escaper": "3.0.3", "http-cache-semantics": "^4.1.1", "import-meta-resolve": "^4.1.0", "js-yaml": "^4.1.0", "kleur": "^4.1.5", "magic-string": "^0.30.17", "magicast": "^0.3.5", "mrmime": "^2.0.1", "neotraverse": "^0.6.18", "p-limit": "^6.2.0", "p-queue": "^8.1.0", "package-manager-detector": "^1.1.0", "picomatch": "^4.0.2", "prompts": "^2.4.2", "rehype": "^13.0.2", "semver": "^7.7.1", "shiki": "^3.2.1", "smol-toml": "^1.3.4", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.12", "tsconfck": "^3.1.5", "ultrahtml": "^1.6.0", "unifont": "~0.5.0", "unist-util-visit": "^5.0.0", "unstorage": "^1.15.0", "vfile": "^6.0.3", "vite": "^6.3.4", "vitefu": "^1.0.6", "xxhash-wasm": "^1.1.0", "yargs-parser": "^21.1.1", "yocto-spinner": "^0.2.1", "zod": "^3.24.2", "zod-to-json-schema": "^3.24.5", "zod-to-ts": "^1.2.0", "@astrojs/markdown-remark": "6.3.3", "@astrojs/telemetry": "3.3.0", "@astrojs/internal-helpers": "0.6.1"}, "optionalDependencies": {"sharp": "^0.33.3"}, "devDependencies": {"@astrojs/check": "^0.9.4", "@playwright/test": "^1.51.1", "@types/aria-query": "^5.0.4", "@types/common-ancestor-path": "^1.0.2", "@types/cssesc": "^3.0.2", "@types/debug": "^4.1.12", "@types/diff": "^5.2.3", "@types/dlv": "^1.1.5", "@types/hast": "^3.0.4", "@types/html-escaper": "3.0.4", "@types/http-cache-semantics": "^4.0.4", "@types/js-yaml": "^4.0.9", "@types/picomatch": "^3.0.2", "@types/prompts": "^2.4.9", "@types/semver": "^7.7.0", "@types/yargs-parser": "^21.0.3", "cheerio": "1.0.0", "eol": "^0.10.0", "execa": "^8.0.1", "expect-type": "^1.2.0", "fs-fixture": "^2.7.1", "mdast-util-mdx": "^3.0.0", "mdast-util-mdx-jsx": "^3.2.0", "node-mocks-http": "^1.16.2", "parse-srcset": "^1.0.2", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "rehype-toc": "^3.0.2", "remark-code-titles": "^0.1.2", "rollup": "^4.37.0", "sass": "^1.86.0", "typescript": "^5.8.3", "undici": "^7.5.0", "unified": "^11.0.5", "vitest": "^3.0.9", "astro-scripts": "0.0.14"}, "engines": {"node": "18.20.8 || ^20.3.0 || >=22.0.0", "npm": ">=9.6.5", "pnpm": ">=7.1.0"}, "publishConfig": {"provenance": true}, "funding": {"type": "opencollective", "url": "https://opencollective.com/astrodotbuild"}, "scripts": {"prebuild": "astro-scripts prebuild --to-string \"src/runtime/server/astro-island.ts\" \"src/runtime/client/{idle,load,media,only,visible}.ts\"", "build": "pnpm run prebuild && astro-scripts build \"src/**/*.{ts,js}\" --copy-wasm && tsc && astro-check --root ./components", "build:ci": "pnpm run prebuild && astro-scripts build \"src/**/*.{ts,js}\" --copy-wasm", "dev": "astro-scripts dev --copy-wasm --prebuild \"src/runtime/server/astro-island.ts\" --prebuild \"src/runtime/client/{idle,load,media,only,visible}.ts\" \"src/**/*.{ts,js}\"", "test": "pnpm run test:unit && pnpm run test:integration && pnpm run test:types", "test:match": "astro-scripts test \"test/**/*.test.js\" --match", "test:e2e": "pnpm test:e2e:chrome && pnpm test:e2e:firefox", "test:e2e:match": "playwright test -g", "test:e2e:chrome": "playwright test", "test:e2e:firefox": "playwright test --config playwright.firefox.config.js", "test:types": "tsc --project test/types/tsconfig.json", "test:unit": "astro-scripts test \"test/units/**/*.test.js\" --teardown ./test/units/teardown.js", "test:integration": "astro-scripts test \"test/*.test.js\""}}