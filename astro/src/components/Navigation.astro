---
import { deckoraCarpentry } from '../data/companies/deckora-carpentry';

const company = deckoraCarpentry;
const currentPath = Astro.url.pathname;

function isActive(path: string): boolean {
  if (path === '/' && currentPath === '/') return true;
  if (path !== '/' && currentPath.startsWith(path)) return true;
  return false;
}
---

<nav class="bg-white shadow-lg sticky top-0 z-50">
  <div class="container mx-auto px-4">
    <div class="flex justify-between items-center py-4">
      <!-- Logo/Company Name -->
      <div class="flex items-center">
        <a href="/" class="text-2xl font-bold text-gray-900 hover:text-blue-600 transition-colors">
          {company.name}
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:flex items-center space-x-8">
        <a 
          href="/" 
          class={`font-medium transition-colors ${isActive('/') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
        >
          Home
        </a>
        
        <!-- Services Dropdown -->
        <div class="relative group">
          <a 
            href="/services" 
            class={`font-medium transition-colors flex items-center ${isActive('/services') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
          >
            Services
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </a>
          
          <!-- Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-64 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <div class="py-2">
              <a href="/services" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                All Services
              </a>
              <div class="border-t border-gray-100 my-2"></div>
              {company.services.slice(0, 6).map(service => (
                <a 
                  href={`/services/${service.slug}`}
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                >
                  {service.name}
                </a>
              ))}
              {company.services.length > 6 && (
                <a href="/services" class="block px-4 py-2 text-sm text-blue-600 font-medium hover:bg-blue-50">
                  View All Services →
                </a>
              )}
            </div>
          </div>
        </div>

        <!-- Locations Dropdown -->
        <div class="relative group">
          <a 
            href="/locations" 
            class={`font-medium transition-colors flex items-center ${isActive('/locations') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
          >
            Locations
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </a>
          
          <!-- Dropdown Menu -->
          <div class="absolute left-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <div class="py-2">
              <a href="/locations" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600">
                All Locations
              </a>
              <div class="border-t border-gray-100 my-2"></div>
              {company.serviceAreas.map(location => (
                <a 
                  href={`/locations/${location.slug}`}
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600"
                >
                  {location.name}
                </a>
              ))}
            </div>
          </div>
        </div>

        <!-- Contact -->
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors"
        >
          {company.phone}
        </a>
      </div>

      <!-- Mobile Menu Button -->
      <div class="md:hidden">
        <button 
          id="mobile-menu-button"
          class="text-gray-700 hover:text-blue-600 focus:outline-none focus:text-blue-600"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="md:hidden hidden pb-4">
      <div class="space-y-2">
        <a 
          href="/" 
          class={`block py-2 font-medium transition-colors ${isActive('/') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
        >
          Home
        </a>
        
        <div>
          <a 
            href="/services" 
            class={`block py-2 font-medium transition-colors ${isActive('/services') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
          >
            Services
          </a>
          <div class="ml-4 space-y-1">
            {company.services.slice(0, 4).map(service => (
              <a 
                href={`/services/${service.slug}`}
                class="block py-1 text-sm text-gray-600 hover:text-blue-600"
              >
                {service.name}
              </a>
            ))}
          </div>
        </div>
        
        <div>
          <a 
            href="/locations" 
            class={`block py-2 font-medium transition-colors ${isActive('/locations') ? 'text-blue-600' : 'text-gray-700 hover:text-blue-600'}`}
          >
            Locations
          </a>
          <div class="ml-4 space-y-1">
            {company.serviceAreas.slice(0, 4).map(location => (
              <a 
                href={`/locations/${location.slug}`}
                class="block py-1 text-sm text-gray-600 hover:text-blue-600"
              >
                {location.name}
              </a>
            ))}
          </div>
        </div>
        
        <a 
          href={`tel:${company.phone}`}
          class="block bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center mt-4"
        >
          Call {company.phone}
        </a>
      </div>
    </div>
  </div>
</nav>

<script>
  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });
  }
</script>

<style>
  .container {
    max-width: 1200px;
  }
</style>
