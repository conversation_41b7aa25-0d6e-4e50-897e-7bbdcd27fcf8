---
import { deckoraCarpentry } from '../data/companies/deckora-carpentry';

const company = deckoraCarpentry;
const currentYear = new Date().getFullYear();
---

<footer class="bg-gray-900 text-white">
  <div class="container mx-auto px-4 py-12">
    <div class="grid md:grid-cols-4 gap-8">
      <!-- Company Info -->
      <div class="md:col-span-1">
        <h3 class="text-xl font-bold mb-4">{company.name}</h3>
        <p class="text-gray-300 mb-4">
          {company.description}
        </p>
        <div class="space-y-2 text-gray-300">
          <p>
            <a href={`tel:${company.phone}`} class="hover:text-white transition-colors">
              📞 {company.phone}
            </a>
          </p>
          <p>
            <a href={`mailto:${company.email}`} class="hover:text-white transition-colors">
              ✉️ {company.email}
            </a>
          </p>
          <p>
            📍 {company.address.city}, {company.address.state}
          </p>
        </div>
      </div>

      <!-- Services -->
      <div>
        <h4 class="text-lg font-semibold mb-4">Our Services</h4>
        <ul class="space-y-2">
          {company.services.map(service => (
            <li>
              <a 
                href={`/services/${service.slug}`}
                class="text-gray-300 hover:text-white transition-colors"
              >
                {service.name}
              </a>
            </li>
          ))}
        </ul>
      </div>

      <!-- Service Areas -->
      <div>
        <h4 class="text-lg font-semibold mb-4">Service Areas</h4>
        <ul class="space-y-2">
          {company.serviceAreas.map(location => (
            <li>
              <a 
                href={`/locations/${location.slug}`}
                class="text-gray-300 hover:text-white transition-colors"
              >
                {location.name}
              </a>
            </li>
          ))}
        </ul>
      </div>

      <!-- Quick Links & Hours -->
      <div>
        <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
        <ul class="space-y-2 mb-6">
          <li>
            <a href="/" class="text-gray-300 hover:text-white transition-colors">
              Home
            </a>
          </li>
          <li>
            <a href="/services" class="text-gray-300 hover:text-white transition-colors">
              All Services
            </a>
          </li>
          <li>
            <a href="/locations" class="text-gray-300 hover:text-white transition-colors">
              All Locations
            </a>
          </li>
        </ul>

        {company.businessHours && (
          <div>
            <h5 class="font-semibold mb-2">Business Hours</h5>
            <div class="text-sm text-gray-300 space-y-1">
              {Object.entries(company.businessHours).map(([day, hours]) => (
                <div class="flex justify-between">
                  <span>{day.substring(0, 3)}</span>
                  <span>{hours}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>

    <!-- Service-Location Grid -->
    <div class="mt-12 pt-8 border-t border-gray-700">
      <h4 class="text-lg font-semibold mb-6 text-center">Services by Location</h4>
      <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 text-sm">
        {company.services.slice(0, 6).map(service => 
          company.serviceAreas.slice(0, 4).map(location => (
            <a 
              href={`/services/${service.slug}/${location.slug}`}
              class="text-gray-400 hover:text-white transition-colors text-xs py-1"
            >
              {service.name} in {location.name}
            </a>
          ))
        )}
      </div>
    </div>

    <!-- Social Media & Copyright -->
    <div class="mt-8 pt-8 border-t border-gray-700 flex flex-col md:flex-row justify-between items-center">
      <div class="text-gray-400 text-sm mb-4 md:mb-0">
        © {currentYear} {company.name}. All rights reserved.
      </div>
      
      {company.socialMedia && (
        <div class="flex space-x-4">
          {company.socialMedia.facebook && (
            <a 
              href={company.socialMedia.facebook}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="Facebook"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
            </a>
          )}
          {company.socialMedia.instagram && (
            <a 
              href={company.socialMedia.instagram}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="Instagram"
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323c.875-.807 2.026-1.297 3.323-1.297s2.448.49 3.323 1.297c.807.875 1.297 2.026 1.297 3.323s-.49 2.448-1.297 3.323c-.875.807-2.026 1.297-3.323 1.297z"/>
              </svg>
            </a>
          )}
        </div>
      )}
    </div>
  </div>
</footer>

<style>
  .container {
    max-width: 1200px;
  }
</style>
