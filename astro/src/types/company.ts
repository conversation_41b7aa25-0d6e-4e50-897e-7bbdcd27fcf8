export interface Service {
  id: string;
  name: string;
  description: string;
  slug: string;
  keywords: string[];
  longDescription?: string;
}

export interface Location {
  id: string;
  name: string;
  slug: string;
  county?: string;
  state: string;
  zipCodes?: string[];
  coordinates?: {
    lat: number;
    lng: number;
  };
}

export interface Company {
  id: string;
  name: string;
  slug: string;
  description: string;
  phone: string;
  email: string;
  website?: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  services: Service[];
  serviceAreas: Location[];
  businessHours?: {
    [key: string]: string;
  };
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
  };
}

export interface SEOData {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl?: string;
  ogImage?: string;
}
