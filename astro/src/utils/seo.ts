import type { Company, Service, Location } from '../types/company';

export function generateLocalBusinessSchema(company: Company) {
  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": company.name,
    "description": company.description,
    "url": company.website,
    "telephone": company.phone,
    "email": company.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": company.address.street,
      "addressLocality": company.address.city,
      "addressRegion": company.address.state,
      "postalCode": company.address.zipCode,
      "addressCountry": "US"
    },
    "geo": company.serviceAreas[0]?.coordinates ? {
      "@type": "GeoCoordinates",
      "latitude": company.serviceAreas[0].coordinates.lat,
      "longitude": company.serviceAreas[0].coordinates.lng
    } : undefined,
    "areaServed": company.serviceAreas.map(area => ({
      "@type": "City",
      "name": area.name,
      "addressRegion": area.state,
      "addressCountry": "US"
    })),
    "serviceType": company.services.map(service => service.name),
    "openingHours": company.businessHours ? Object.entries(company.businessHours).map(([day, hours]) => 
      hours !== 'Closed' ? `${day.substring(0, 2)} ${hours}` : null
    ).filter(Boolean) : undefined,
    "sameAs": company.socialMedia ? Object.values(company.socialMedia).filter(Boolean) : undefined
  };
}

export function generateServiceSchema(company: Company, service: Service, location?: Location) {
  const baseSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": service.name,
    "description": service.longDescription || service.description,
    "provider": {
      "@type": "LocalBusiness",
      "name": company.name,
      "telephone": company.phone,
      "email": company.email,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": company.address.street,
        "addressLocality": company.address.city,
        "addressRegion": company.address.state,
        "postalCode": company.address.zipCode,
        "addressCountry": "US"
      }
    },
    "serviceType": service.name,
    "category": service.keywords.join(', ')
  };

  if (location) {
    return {
      ...baseSchema,
      "areaServed": {
        "@type": "City",
        "name": location.name,
        "addressRegion": location.state,
        "addressCountry": "US"
      }
    };
  }

  return {
    ...baseSchema,
    "areaServed": company.serviceAreas.map(area => ({
      "@type": "City",
      "name": area.name,
      "addressRegion": area.state,
      "addressCountry": "US"
    }))
  };
}

export function generateBreadcrumbSchema(breadcrumbs: Array<{name: string, url: string}>) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
}

export function generateSEOTitle(service: Service, location: Location, company: Company): string {
  return `${service.name} in ${location.name}, ${location.state} | ${company.name}`;
}

export function generateSEODescription(service: Service, location: Location, company: Company): string {
  return `Professional ${service.name.toLowerCase()} services in ${location.name}, ${location.state}. ${service.description} Contact ${company.name} for a free estimate. Licensed and insured.`;
}

export function generateLocationSEOTitle(location: Location, company: Company): string {
  return `${company.name} Services in ${location.name}, ${location.state} | Home Improvement Contractors`;
}

export function generateLocationSEODescription(location: Location, company: Company): string {
  const serviceList = company.services.slice(0, 3).map(s => s.name.toLowerCase()).join(', ');
  return `Professional home improvement services in ${location.name}, ${location.state}. Specializing in ${serviceList} and more. Licensed contractors serving ${location.county || location.name}.`;
}

export function generateServiceSEOKeywords(service: Service, location?: Location): string[] {
  const baseKeywords = [...service.keywords];
  
  if (location) {
    baseKeywords.push(
      `${service.name.toLowerCase()} ${location.name}`,
      `${service.name.toLowerCase()} ${location.state}`,
      `${location.name} ${service.name.toLowerCase()}`,
      `${location.name} contractors`,
      `${location.name} home improvement`
    );
    
    if (location.county) {
      baseKeywords.push(
        `${service.name.toLowerCase()} ${location.county}`,
        `${location.county} contractors`
      );
    }
  }
  
  return baseKeywords;
}
