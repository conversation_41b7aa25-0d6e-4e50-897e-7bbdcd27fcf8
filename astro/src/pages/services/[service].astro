---
import type { GetStaticPaths } from 'astro';
import Layout from '../../layouts/Layout.astro';
import { deckoraCarpentry } from '../../data/companies/deckora-carpentry';
import type { Service } from '../../types/company';

export const getStaticPaths: GetStaticPaths = () => {
  return deckoraCarpentry.services.map(service => ({
    params: { service: service.slug },
    props: { service, company: deckoraCarpentry }
  }));
};

interface Props {
  service: Service;
  company: typeof deckoraCarpentry;
}

const { service, company } = Astro.props;

const pageTitle = `${service.name} Services | ${company.name}`;
const pageDescription = `Professional ${service.name.toLowerCase()} services throughout Georgia. ${service.description} Contact ${company.name} for a free estimate.`;
---

<Layout title={pageTitle}>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb mb-6">
      <ol class="flex space-x-2 text-sm text-gray-600">
        <li><a href="/" class="hover:text-blue-600">Home</a></li>
        <li class="before:content-['/'] before:mx-2">
          <a href="/services" class="hover:text-blue-600">Services</a>
        </li>
        <li class="before:content-['/'] before:mx-2 text-gray-900">{service.name}</li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="hero mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {service.name} Services
      </h1>
      <p class="text-xl text-gray-700 mb-6 max-w-3xl">
        {service.longDescription || service.description}
      </p>
      <div class="flex flex-col sm:flex-row gap-4">
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center"
        >
          Call {company.phone}
        </a>
        <a 
          href="#contact"
          class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center"
        >
          Get Free Estimate
        </a>
      </div>
    </section>

    <!-- Service Areas -->
    <section class="service-areas mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">
        {service.name} Service Areas
      </h2>
      <p class="text-lg text-gray-700 mb-6">
        We provide {service.name.toLowerCase()} services throughout the following areas:
      </p>
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {company.serviceAreas.map(location => (
          <a 
            href={`/services/${service.slug}/${location.slug}`}
            class="block p-6 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
          >
            <h3 class="font-semibold text-gray-900 mb-2">
              {service.name} in {location.name}
            </h3>
            <p class="text-sm text-gray-600">
              Professional {service.name.toLowerCase()} services in {location.name}, {location.state}
            </p>
            <span class="text-blue-600 text-sm font-medium mt-2 inline-block">
              Learn More →
            </span>
          </a>
        ))}
      </div>
    </section>

    <!-- Why Choose Us -->
    <section class="why-choose-us mb-12">
      <div class="bg-gray-50 p-8 rounded-lg">
        <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
          Why Choose {company.name}?
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div class="text-center">
            <div class="text-4xl mb-4">🏆</div>
            <h3 class="font-semibold text-gray-900 mb-2">Expert Craftsmanship</h3>
            <p class="text-gray-600">Years of experience delivering quality {service.name.toLowerCase()} projects</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4">📋</div>
            <h3 class="font-semibold text-gray-900 mb-2">Licensed & Insured</h3>
            <p class="text-gray-600">Fully licensed and insured for your peace of mind</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4">💰</div>
            <h3 class="font-semibold text-gray-900 mb-2">Free Estimates</h3>
            <p class="text-gray-600">No-obligation estimates for all {service.name.toLowerCase()} projects</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Other Services -->
    <section class="other-services mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">
        Our Other Services
      </h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {company.services
          .filter(s => s.id !== service.id)
          .map(otherService => (
            <a 
              href={`/services/${otherService.slug}`}
              class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
            >
              <h3 class="font-semibold text-gray-900 mb-2">{otherService.name}</h3>
              <p class="text-sm text-gray-600">{otherService.description}</p>
            </a>
          ))
        }
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact bg-blue-50 p-8 rounded-lg">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Ready to Get Started?
        </h2>
        <p class="text-xl text-gray-700 mb-6">
          Contact {company.name} today for your {service.name.toLowerCase()} project.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href={`tel:${company.phone}`}
            class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Call {company.phone}
          </a>
          <a 
            href={`mailto:${company.email}`}
            class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
          >
            Email Us
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
