---
import type { GetStaticPaths } from 'astro';
import Layout from '../../../layouts/Layout.astro';
import { deckoraCarpentry } from '../../../data/companies/deckora-carpentry';
import type { Service, Location } from '../../../types/company';
import {
  generateServiceSchema,
  generateBreadcrumbSchema,
  generateSEOTitle,
  generateSEODescription,
  generateServiceSEOKeywords
} from '../../../utils/seo';

export const getStaticPaths: GetStaticPaths = () => {
  const paths = [];

  // Generate a page for each service-location combination
  for (const service of deckoraCarpentry.services) {
    for (const location of deckoraCarpentry.serviceAreas) {
      paths.push({
        params: {
          service: service.slug,
          location: location.slug
        },
        props: {
          service,
          location,
          company: deckoraCarpentry
        }
      });
    }
  }

  return paths;
};

interface Props {
  service: Service;
  location: Location;
  company: typeof deckoraCarpentry;
}

const { service, location, company } = Astro.props;

// Generate SEO data
const pageTitle = generateSEOTitle(service, location, company);
const pageDescription = generateSEODescription(service, location, company);
const keywords = generateServiceSEOKeywords(service, location);
const canonicalUrl = `${company.website || ''}/services/${service.slug}/${location.slug}`;

// Generate structured data
const serviceSchema = generateServiceSchema(company, service, location);
const breadcrumbSchema = generateBreadcrumbSchema([
  { name: 'Home', url: '/' },
  { name: 'Services', url: '/services' },
  { name: service.name, url: `/services/${service.slug}` },
  { name: location.name, url: `/services/${service.slug}/${location.slug}` }
]);

const structuredData = {
  "@context": "https://schema.org",
  "@graph": [serviceSchema, breadcrumbSchema]
};
---

<Layout
  title={pageTitle}
  description={pageDescription}
  keywords={keywords}
  canonicalUrl={canonicalUrl}
  structuredData={structuredData}
>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb mb-6">
      <ol class="flex space-x-2 text-sm text-gray-600">
        <li><a href="/" class="hover:text-blue-600">Home</a></li>
        <li class="before:content-['/'] before:mx-2">
          <a href="/services" class="hover:text-blue-600">Services</a>
        </li>
        <li class="before:content-['/'] before:mx-2">
          <a href={`/services/${service.slug}`} class="hover:text-blue-600">{service.name}</a>
        </li>
        <li class="before:content-['/'] before:mx-2 text-gray-900">{location.name}</li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="hero mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {service.name} in {location.name}, {location.state}
      </h1>
      <p class="text-xl text-gray-700 mb-6 max-w-3xl">
        {service.longDescription || service.description}
      </p>
      <div class="flex flex-col sm:flex-row gap-4">
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center"
        >
          Call {company.phone}
        </a>
        <a 
          href="#contact"
          class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center"
        >
          Get Free Estimate
        </a>
      </div>
    </section>

    <!-- Service Details -->
    <section class="service-details mb-12">
      <div class="grid md:grid-cols-2 gap-8">
        <div>
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            Why Choose {company.name} for {service.name}?
          </h2>
          <ul class="space-y-3 text-gray-700">
            <li class="flex items-start">
              <span class="text-green-500 mr-2">✓</span>
              Licensed and insured professionals
            </li>
            <li class="flex items-start">
              <span class="text-green-500 mr-2">✓</span>
              Free estimates and consultations
            </li>
            <li class="flex items-start">
              <span class="text-green-500 mr-2">✓</span>
              Quality materials and workmanship
            </li>
            <li class="flex items-start">
              <span class="text-green-500 mr-2">✓</span>
              Serving {location.name} and surrounding areas
            </li>
            <li class="flex items-start">
              <span class="text-green-500 mr-2">✓</span>
              Satisfaction guarantee
            </li>
          </ul>
        </div>
        
        <div class="bg-gray-50 p-6 rounded-lg">
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Service Area</h3>
          <p class="text-gray-700 mb-4">
            We proudly serve {location.name} and the surrounding {location.county} area.
          </p>
          {location.zipCodes && (
            <div>
              <h4 class="font-semibold text-gray-900 mb-2">Zip Codes Served:</h4>
              <p class="text-gray-700">{location.zipCodes.join(', ')}</p>
            </div>
          )}
        </div>
      </div>
    </section>

    <!-- Other Services -->
    <section class="other-services mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">
        Other Services in {location.name}
      </h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {company.services
          .filter(s => s.id !== service.id)
          .slice(0, 6)
          .map(otherService => (
            <a 
              href={`/services/${otherService.slug}/${location.slug}`}
              class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
            >
              <h3 class="font-semibold text-gray-900 mb-2">{otherService.name}</h3>
              <p class="text-sm text-gray-600">{otherService.description}</p>
            </a>
          ))
        }
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact bg-blue-50 p-8 rounded-lg">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Ready to Get Started?
        </h2>
        <p class="text-xl text-gray-700 mb-6">
          Contact {company.name} today for your {service.name.toLowerCase()} project in {location.name}.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href={`tel:${company.phone}`}
            class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Call {company.phone}
          </a>
          <a 
            href={`mailto:${company.email}`}
            class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
          >
            Email Us
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
