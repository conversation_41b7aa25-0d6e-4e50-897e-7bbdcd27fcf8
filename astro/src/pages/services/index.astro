---
import Layout from '../../layouts/Layout.astro';
import { deckoraCarpentry } from '../../data/companies/deckora-carpentry';

const company = deckoraCarpentry;
const pageTitle = `Our Services | ${company.name}`;
const pageDescription = `Professional home improvement services including ${company.services.slice(0, 3).map(s => s.name.toLowerCase()).join(', ')} and more. Licensed contractors serving Georgia.`;
---

<Layout title={pageTitle}>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb mb-6">
      <ol class="flex space-x-2 text-sm text-gray-600">
        <li><a href="/" class="hover:text-blue-600">Home</a></li>
        <li class="before:content-['/'] before:mx-2 text-gray-900">Services</li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="hero mb-12 text-center">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Our Professional Services
      </h1>
      <p class="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
        {company.name} offers comprehensive home improvement and carpentry services 
        throughout Georgia. From small repairs to major renovations, we're your trusted local contractors.
      </p>
      <a 
        href={`tel:${company.phone}`}
        class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
      >
        Call {company.phone} for Free Estimate
      </a>
    </section>

    <!-- Services Grid -->
    <section class="services-grid mb-12">
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {company.services.map(service => (
          <div class="service-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
              <a href={`/services/${service.slug}`} class="hover:text-blue-600">
                {service.name}
              </a>
            </h2>
            <p class="text-gray-700 mb-4">
              {service.description}
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              {service.keywords.slice(0, 3).map(keyword => (
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {keyword}
                </span>
              ))}
            </div>
            <div class="space-y-2">
              <a 
                href={`/services/${service.slug}`}
                class="block text-blue-600 hover:text-blue-700 font-medium"
              >
                Learn More About {service.name} →
              </a>
              <div class="text-sm text-gray-600">
                <strong>Available in:</strong>
                <div class="mt-1 space-y-1">
                  {company.serviceAreas.slice(0, 4).map(location => (
                    <a 
                      href={`/services/${service.slug}/${location.slug}`}
                      class="block hover:text-blue-600"
                    >
                      • {location.name}
                    </a>
                  ))}
                  {company.serviceAreas.length > 4 && (
                    <a 
                      href={`/services/${service.slug}`}
                      class="block text-blue-600 hover:text-blue-700"
                    >
                      + {company.serviceAreas.length - 4} more locations
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </section>

    <!-- Why Choose Us -->
    <section class="why-choose-us mb-12">
      <div class="bg-gray-50 p-8 rounded-lg">
        <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
          Why Choose {company.name}?
        </h2>
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="text-center">
            <div class="text-4xl mb-4">🏆</div>
            <h3 class="font-semibold text-gray-900 mb-2">Expert Craftsmanship</h3>
            <p class="text-gray-600">Years of experience in home improvement and carpentry</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4">📋</div>
            <h3 class="font-semibold text-gray-900 mb-2">Licensed & Insured</h3>
            <p class="text-gray-600">Fully licensed and insured for your protection</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4">💰</div>
            <h3 class="font-semibold text-gray-900 mb-2">Free Estimates</h3>
            <p class="text-gray-600">No-obligation estimates for all projects</p>
          </div>
          <div class="text-center">
            <div class="text-4xl mb-4">⭐</div>
            <h3 class="font-semibold text-gray-900 mb-2">Satisfaction Guarantee</h3>
            <p class="text-gray-600">We stand behind our work with a satisfaction guarantee</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Service Areas -->
    <section class="service-areas mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
        Areas We Serve
      </h2>
      <p class="text-lg text-gray-700 mb-8 text-center max-w-3xl mx-auto">
        We proudly serve customers throughout the Golden Isles area of Georgia and surrounding counties.
      </p>
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        {company.serviceAreas.map(location => (
          <a 
            href={`/locations/${location.slug}`}
            class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all text-center"
          >
            <h3 class="font-semibold text-gray-900 mb-2">
              {location.name}
            </h3>
            <p class="text-sm text-gray-600">
              {location.county && `${location.county}, `}{location.state}
            </p>
          </a>
        ))}
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact bg-blue-50 p-8 rounded-lg text-center">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">
        Ready to Start Your Project?
      </h2>
      <p class="text-xl text-gray-700 mb-6">
        Contact {company.name} today for professional home improvement services.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Call {company.phone}
        </a>
        <a 
          href={`mailto:${company.email}`}
          class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
        >
          Email Us
        </a>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
