---
import Layout from '../../layouts/Layout.astro';
import { deckoraCarpentry } from '../../data/companies/deckora-carpentry';

const company = deckoraCarpentry;
const pageTitle = `Service Areas | ${company.name}`;
const pageDescription = `${company.name} serves ${company.serviceAreas.map(l => l.name).join(', ')} and surrounding areas in Georgia. Professional home improvement services in your area.`;
---

<Layout title={pageTitle}>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb mb-6">
      <ol class="flex space-x-2 text-sm text-gray-600">
        <li><a href="/" class="hover:text-blue-600">Home</a></li>
        <li class="before:content-['/'] before:mx-2 text-gray-900">Locations</li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="hero mb-12 text-center">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        Areas We Serve
      </h1>
      <p class="text-xl text-gray-700 mb-8 max-w-3xl mx-auto">
        {company.name} proudly serves customers throughout the Golden Isles area of Georgia. 
        We're your local home improvement and carpentry experts, committed to serving our community.
      </p>
      <a 
        href={`tel:${company.phone}`}
        class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
      >
        Call {company.phone} for Service in Your Area
      </a>
    </section>

    <!-- Service Areas Grid -->
    <section class="service-areas mb-12">
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {company.serviceAreas.map(location => (
          <div class="location-card bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">
              <a href={`/locations/${location.slug}`} class="hover:text-blue-600">
                {location.name}
              </a>
            </h2>
            <div class="space-y-2 mb-4 text-gray-700">
              {location.county && (
                <p><strong>County:</strong> {location.county}</p>
              )}
              <p><strong>State:</strong> {location.state}</p>
              {location.zipCodes && (
                <p><strong>Zip Codes:</strong> {location.zipCodes.slice(0, 3).join(', ')}{location.zipCodes.length > 3 ? '...' : ''}</p>
              )}
            </div>
            
            <div class="mb-4">
              <h3 class="font-semibold text-gray-900 mb-2">Services Available:</h3>
              <div class="space-y-1">
                {company.services.slice(0, 4).map(service => (
                  <a 
                    href={`/services/${service.slug}/${location.slug}`}
                    class="block text-sm text-blue-600 hover:text-blue-700"
                  >
                    • {service.name}
                  </a>
                ))}
                {company.services.length > 4 && (
                  <a 
                    href={`/locations/${location.slug}`}
                    class="block text-sm text-blue-600 hover:text-blue-700 font-medium"
                  >
                    + {company.services.length - 4} more services
                  </a>
                )}
              </div>
            </div>

            <a 
              href={`/locations/${location.slug}`}
              class="inline-block bg-blue-600 text-white px-4 py-2 rounded font-medium hover:bg-blue-700 transition-colors"
            >
              View {location.name} Services
            </a>
          </div>
        ))}
      </div>
    </section>

    <!-- Coverage Map Info -->
    <section class="coverage-info mb-12">
      <div class="bg-gray-50 p-8 rounded-lg">
        <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
          Our Service Coverage
        </h2>
        <div class="grid md:grid-cols-2 gap-8">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Primary Service Areas</h3>
            <ul class="space-y-2 text-gray-700">
              {company.serviceAreas.filter(l => l.county === 'Glynn County').map(location => (
                <li class="flex items-center">
                  <span class="text-green-500 mr-2">✓</span>
                  <a href={`/locations/${location.slug}`} class="hover:text-blue-600">
                    {location.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-4">Extended Service Areas</h3>
            <ul class="space-y-2 text-gray-700">
              {company.serviceAreas.filter(l => l.county !== 'Glynn County').map(location => (
                <li class="flex items-center">
                  <span class="text-blue-500 mr-2">✓</span>
                  <a href={`/locations/${location.slug}`} class="hover:text-blue-600">
                    {location.name} ({location.county})
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div class="mt-6 text-center">
          <p class="text-gray-700">
            Don't see your area listed? <a href={`tel:${company.phone}`} class="text-blue-600 hover:text-blue-700">Call us</a> - we may still be able to help!
          </p>
        </div>
      </div>
    </section>

    <!-- Why Choose Local -->
    <section class="why-local mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-8 text-center">
        Why Choose a Local Contractor?
      </h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-4xl mb-4">🚗</div>
          <h3 class="font-semibold text-gray-900 mb-2">Fast Response</h3>
          <p class="text-gray-600">Quick response times throughout our service areas</p>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-4">🏛️</div>
          <h3 class="font-semibold text-gray-900 mb-2">Local Knowledge</h3>
          <p class="text-gray-600">Familiar with local building codes and regulations</p>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-4">🤝</div>
          <h3 class="font-semibold text-gray-900 mb-2">Community Focused</h3>
          <p class="text-gray-600">Committed to serving our local community</p>
        </div>
        <div class="text-center">
          <div class="text-4xl mb-4">🔧</div>
          <h3 class="font-semibold text-gray-900 mb-2">Local Suppliers</h3>
          <p class="text-gray-600">Established relationships with area suppliers</p>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact bg-blue-50 p-8 rounded-lg text-center">
      <h2 class="text-3xl font-bold text-gray-900 mb-4">
        Ready to Get Started?
      </h2>
      <p class="text-xl text-gray-700 mb-6">
        Contact {company.name} today for professional service in your area.
      </p>
      <div class="grid md:grid-cols-3 gap-6 mb-8">
        <div>
          <h3 class="font-semibold text-gray-900 mb-2">Phone</h3>
          <a href={`tel:${company.phone}`} class="text-blue-600 hover:text-blue-700 text-lg">
            {company.phone}
          </a>
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 mb-2">Email</h3>
          <a href={`mailto:${company.email}`} class="text-blue-600 hover:text-blue-700">
            {company.email}
          </a>
        </div>
        <div>
          <h3 class="font-semibold text-gray-900 mb-2">Main Office</h3>
          <p class="text-gray-700">
            {company.address.city}, {company.address.state}
          </p>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
        >
          Call Now
        </a>
        <a 
          href={`mailto:${company.email}`}
          class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
        >
          Email Us
        </a>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
