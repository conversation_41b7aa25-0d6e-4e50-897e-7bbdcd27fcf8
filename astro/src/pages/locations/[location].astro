---
import type { GetStaticPaths } from 'astro';
import Layout from '../../layouts/Layout.astro';
import { deckoraCarpentry } from '../../data/companies/deckora-carpentry';
import type { Location } from '../../types/company';

export const getStaticPaths: GetStaticPaths = () => {
  return deckoraCarpentry.serviceAreas.map(location => ({
    params: { location: location.slug },
    props: { location, company: deckoraCarpentry }
  }));
};

interface Props {
  location: Location;
  company: typeof deckoraCarpentry;
}

const { location, company } = Astro.props;

const pageTitle = `${company.name} Services in ${location.name}, ${location.state}`;
const pageDescription = `Professional home improvement and carpentry services in ${location.name}, ${location.state}. Licensed contractors serving ${location.county}. Call for a free estimate.`;
---

<Layout title={pageTitle}>
  <main class="container mx-auto px-4 py-8">
    <!-- Breadcrumb Navigation -->
    <nav class="breadcrumb mb-6">
      <ol class="flex space-x-2 text-sm text-gray-600">
        <li><a href="/" class="hover:text-blue-600">Home</a></li>
        <li class="before:content-['/'] before:mx-2">
          <a href="/locations" class="hover:text-blue-600">Locations</a>
        </li>
        <li class="before:content-['/'] before:mx-2 text-gray-900">{location.name}</li>
      </ol>
    </nav>

    <!-- Hero Section -->
    <section class="hero mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
        {company.name} Services in {location.name}, {location.state}
      </h1>
      <p class="text-xl text-gray-700 mb-6 max-w-3xl">
        Professional carpentry and home improvement services serving {location.name} and the surrounding {location.county} area. 
        Licensed, insured, and committed to quality workmanship.
      </p>
      <div class="flex flex-col sm:flex-row gap-4">
        <a 
          href={`tel:${company.phone}`}
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors text-center"
        >
          Call {company.phone}
        </a>
        <a 
          href="#contact"
          class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center"
        >
          Get Free Estimate
        </a>
      </div>
    </section>

    <!-- Location Info -->
    <section class="location-info mb-12">
      <div class="bg-gray-50 p-8 rounded-lg">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Serving {location.name} and Surrounding Areas
        </h2>
        <div class="grid md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Service Area Details</h3>
            <ul class="space-y-2 text-gray-700">
              <li><strong>Location:</strong> {location.name}, {location.state}</li>
              {location.county && <li><strong>County:</strong> {location.county}</li>}
              {location.zipCodes && (
                <li><strong>Zip Codes:</strong> {location.zipCodes.join(', ')}</li>
              )}
            </ul>
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Why Choose Local?</h3>
            <ul class="space-y-2 text-gray-700">
              <li>• Fast response times in {location.name}</li>
              <li>• Knowledge of local building codes</li>
              <li>• Established relationships with local suppliers</li>
              <li>• Community-focused service</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Services in Location -->
    <section class="services-in-location mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">
        Our Services in {location.name}
      </h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {company.services.map(service => (
          <a 
            href={`/services/${service.slug}/${location.slug}`}
            class="block p-6 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
          >
            <h3 class="font-semibold text-gray-900 mb-3 text-lg">
              {service.name}
            </h3>
            <p class="text-gray-600 mb-4">
              {service.description}
            </p>
            <div class="flex flex-wrap gap-2 mb-4">
              {service.keywords.slice(0, 3).map(keyword => (
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {keyword}
                </span>
              ))}
            </div>
            <span class="text-blue-600 font-medium">
              Learn More →
            </span>
          </a>
        ))}
      </div>
    </section>

    <!-- Business Hours -->
    {company.businessHours && (
      <section class="business-hours mb-12">
        <div class="bg-blue-50 p-8 rounded-lg">
          <h2 class="text-3xl font-bold text-gray-900 mb-6 text-center">
            Business Hours
          </h2>
          <div class="max-w-md mx-auto">
            {Object.entries(company.businessHours).map(([day, hours]) => (
              <div class="flex justify-between py-2 border-b border-blue-200 last:border-b-0">
                <span class="font-medium text-gray-900">{day}</span>
                <span class="text-gray-700">{hours}</span>
              </div>
            ))}
          </div>
        </div>
      </section>
    )}

    <!-- Other Locations -->
    <section class="other-locations mb-12">
      <h2 class="text-3xl font-bold text-gray-900 mb-6">
        Other Areas We Serve
      </h2>
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
        {company.serviceAreas
          .filter(area => area.id !== location.id)
          .map(otherLocation => (
            <a 
              href={`/locations/${otherLocation.slug}`}
              class="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all"
            >
              <h3 class="font-semibold text-gray-900 mb-2">
                {otherLocation.name}
              </h3>
              <p class="text-sm text-gray-600">
                {otherLocation.county && `${otherLocation.county}, `}{otherLocation.state}
              </p>
            </a>
          ))
        }
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact bg-blue-50 p-8 rounded-lg">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          Ready to Start Your Project in {location.name}?
        </h2>
        <p class="text-xl text-gray-700 mb-6">
          Contact {company.name} today for professional service in {location.name}, {location.state}.
        </p>
        <div class="grid md:grid-cols-3 gap-6 mb-8">
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Phone</h3>
            <a href={`tel:${company.phone}`} class="text-blue-600 hover:text-blue-700">
              {company.phone}
            </a>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Email</h3>
            <a href={`mailto:${company.email}`} class="text-blue-600 hover:text-blue-700">
              {company.email}
            </a>
          </div>
          <div>
            <h3 class="font-semibold text-gray-900 mb-2">Address</h3>
            <p class="text-gray-700">
              {company.address.street}<br>
              {company.address.city}, {company.address.state} {company.address.zipCode}
            </p>
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a 
            href={`tel:${company.phone}`}
            class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Call Now
          </a>
          <a 
            href={`mailto:${company.email}`}
            class="border-2 border-blue-600 text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors"
          >
            Email Us
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
