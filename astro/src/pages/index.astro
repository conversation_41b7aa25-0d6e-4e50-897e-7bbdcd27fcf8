---
import Layout from '../layouts/Layout.astro';
import { deckoraCarpentry } from '../data/companies/deckora-carpentry';
import { generateLocalBusinessSchema } from '../utils/seo';

const company = deckoraCarpentry;
const pageTitle = `${company.name} | Professional Home Improvement Services in Georgia`;
const pageDescription = `${company.description} Serving ${company.serviceAreas.map(l => l.name).slice(0, 3).join(', ')} and surrounding areas. Licensed and insured contractors.`;
const keywords = ['home improvement', 'carpentry', 'contractors', 'Georgia', 'Brunswick', 'Golden Isles', ...company.services.slice(0, 5).map(s => s.name.toLowerCase())];

const structuredData = generateLocalBusinessSchema(company);
---

<Layout
  title={pageTitle}
  description={pageDescription}
  keywords={keywords}
  structuredData={structuredData}
>
  <main>
    <!-- Hero Section -->
    <section class="bg-gradient text-white py-20">
      <div class="container mx-auto px-4 text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          {company.name}
        </h1>
        <p class="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
          Professional Home Improvement & Carpentry Services in the Golden Isles Area
        </p>
        <div class="flex flex-col gap-4 justify-center">
          <a
            href={`tel:${company.phone}`}
            class="btn bg-white text-blue-600 font-bold text-lg"
            style="display: inline-block; margin: 0 auto;"
          >
            Call {company.phone}
          </a>
          <a
            href="#services"
            class="btn btn-outline text-white font-bold text-lg"
            style="display: inline-block; margin: 0 auto; border-color: white; color: white;"
          >
            View Our Services
          </a>
        </div>
      </div>
    </section>

    <!-- Services Preview -->
    <section id="services" class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Professional Services
          </h2>
          <p class="text-xl text-gray-700 max-w-3xl mx-auto">
            From deck construction to complete home renovations, we provide comprehensive home improvement services throughout Georgia.
          </p>
        </div>

        <div class="grid gap-6 mb-12" style="grid-template-columns: repeat(auto-fit, minmax(250px, 1fr))">
          {company.services.slice(0, 8).map(service => (
            <div class="bg-white p-6 rounded-lg shadow-lg hover:shadow-lg transition-colors" style="border: 1px solid #e5e7eb;">
              <h3 class="text-xl font-semibold text-gray-900 mb-3">
                <a href={`/services/${service.slug}`} class="hover:text-blue-600">
                  {service.name}
                </a>
              </h3>
              <p class="text-gray-600 mb-4 text-sm">
                {service.description}
              </p>
              <a
                href={`/services/${service.slug}`}
                class="text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                Learn More →
              </a>
            </div>
          ))}
        </div>

        <div class="text-center">
          <a
            href="/services"
            class="btn btn-primary"
          >
            View All Services
          </a>
        </div>
      </div>
    </section>

    <!-- Service Areas -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Areas We Serve
          </h2>
          <p class="text-xl text-gray-700 max-w-3xl mx-auto">
            Proudly serving the Golden Isles area and surrounding communities in Georgia.
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {company.serviceAreas.map(location => (
            <div class="text-center p-6 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">
                <a href={`/locations/${location.slug}`} class="hover:text-blue-600">
                  {location.name}
                </a>
              </h3>
              <p class="text-gray-600 text-sm mb-3">
                {location.county && `${location.county}, `}{location.state}
              </p>
              <a
                href={`/locations/${location.slug}`}
                class="text-blue-600 hover:text-blue-700 font-medium text-sm"
              >
                View Services →
              </a>
            </div>
          ))}
        </div>

        <div class="text-center">
          <a
            href="/locations"
            class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors inline-block"
          >
            View All Locations
          </a>
        </div>
      </div>
    </section>

    <!-- Why Choose Us -->
    <section class="py-16 bg-blue-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Why Choose {company.name}?
          </h2>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="text-center">
            <div class="text-5xl mb-4">🏆</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Expert Craftsmanship</h3>
            <p class="text-gray-600">Years of experience delivering quality home improvement projects throughout Georgia.</p>
          </div>
          <div class="text-center">
            <div class="text-5xl mb-4">📋</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Licensed & Insured</h3>
            <p class="text-gray-600">Fully licensed and insured for your complete peace of mind and protection.</p>
          </div>
          <div class="text-center">
            <div class="text-5xl mb-4">💰</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Free Estimates</h3>
            <p class="text-gray-600">No-obligation estimates for all home improvement and carpentry projects.</p>
          </div>
          <div class="text-center">
            <div class="text-5xl mb-4">⭐</div>
            <h3 class="text-xl font-semibold text-gray-900 mb-3">Satisfaction Guarantee</h3>
            <p class="text-gray-600">We stand behind our work with a complete satisfaction guarantee.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="py-16 bg-gray-900 text-white">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-6">
          Ready to Start Your Project?
        </h2>
        <p class="text-xl mb-8 max-w-3xl mx-auto">
          Contact {company.name} today for professional home improvement services.
          Free estimates and consultations available.
        </p>

        <div class="grid md:grid-cols-3 gap-8 mb-12">
          <div>
            <h3 class="text-xl font-semibold mb-3">Phone</h3>
            <a href={`tel:${company.phone}`} class="text-2xl text-blue-400 hover:text-blue-300">
              {company.phone}
            </a>
          </div>
          <div>
            <h3 class="text-xl font-semibold mb-3">Email</h3>
            <a href={`mailto:${company.email}`} class="text-xl text-blue-400 hover:text-blue-300">
              {company.email}
            </a>
          </div>
          <div>
            <h3 class="text-xl font-semibold mb-3">Service Area</h3>
            <p class="text-xl">
              {company.address.city}, {company.address.state}<br>
              & Surrounding Areas
            </p>
          </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href={`tel:${company.phone}`}
            class="bg-blue-600 text-white px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-700 transition-colors"
          >
            Call Now for Free Estimate
          </a>
          <a
            href={`mailto:${company.email}`}
            class="border-2 border-blue-400 text-blue-400 px-8 py-4 rounded-lg font-bold text-lg hover:bg-blue-400 hover:text-white transition-colors"
          >
            Email Us
          </a>
        </div>
      </div>
    </section>
  </main>
</Layout>

<style>
  .container {
    max-width: 1200px;
  }
</style>
