---
import Navigation from '../components/Navigation.astro';
import Footer from '../components/Footer.astro';

export interface Props {
  title: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  structuredData?: object;
}

const {
  title,
  description = "Professional home improvement and carpentry services in Georgia. Licensed contractors providing quality workmanship.",
  keywords = ["home improvement", "carpentry", "contractors", "Georgia"],
  canonicalUrl,
  ogImage,
  structuredData
} = Astro.props;

const currentUrl = new URL(Astro.request.url);
const fullCanonicalUrl = canonicalUrl || currentUrl.href;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />

		<!-- Primary Meta Tags -->
		<title>{title}</title>
		<meta name="title" content={title} />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords.join(', ')} />
		<link rel="canonical" href={fullCanonicalUrl} />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={fullCanonicalUrl} />
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		{ogImage && <meta property="og:image" content={ogImage} />}

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={fullCanonicalUrl} />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content={description} />
		{ogImage && <meta property="twitter:image" content={ogImage} />}

		<!-- Local Business Schema -->
		{structuredData && (
			<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
		)}

		<!-- Custom CSS -->
		<style>
			/* Reset and base styles */
			* {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}

			body {
				font-family: system-ui, -apple-system, sans-serif;
				line-height: 1.6;
				color: #1f2937;
			}

			/* Utility classes */
			.container { max-width: 1200px; margin: 0 auto; }
			.mx-auto { margin-left: auto; margin-right: auto; }
			.px-4 { padding-left: 1rem; padding-right: 1rem; }
			.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
			.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
			.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
			.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
			.mb-4 { margin-bottom: 1rem; }
			.mb-6 { margin-bottom: 1.5rem; }
			.mb-8 { margin-bottom: 2rem; }
			.mb-12 { margin-bottom: 3rem; }
			.text-center { text-align: center; }
			.text-white { color: white; }
			.text-gray-900 { color: #1f2937; }
			.text-gray-700 { color: #374151; }
			.text-gray-600 { color: #4b5563; }
			.text-blue-600 { color: #2563eb; }
			.bg-white { background-color: white; }
			.bg-gray-50 { background-color: #f9fafb; }
			.bg-blue-600 { background-color: #2563eb; }
			.bg-blue-50 { background-color: #eff6ff; }
			.bg-gray-900 { background-color: #1f2937; }
			.text-xl { font-size: 1.25rem; }
			.text-2xl { font-size: 1.5rem; }
			.text-3xl { font-size: 1.875rem; }
			.text-4xl { font-size: 2.25rem; }
			.text-5xl { font-size: 3rem; }
			.font-bold { font-weight: 700; }
			.font-semibold { font-weight: 600; }
			.font-medium { font-weight: 500; }
			.rounded-lg { border-radius: 0.5rem; }
			.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
			.border { border: 1px solid #e5e7eb; }
			.block { display: block; }
			.flex { display: flex; }
			.grid { display: grid; }
			.hidden { display: none; }
			.items-center { align-items: center; }
			.justify-center { justify-content: center; }
			.justify-between { justify-content: space-between; }
			.space-x-4 > * + * { margin-left: 1rem; }
			.space-x-8 > * + * { margin-left: 2rem; }
			.space-y-2 > * + * { margin-top: 0.5rem; }
			.space-y-4 > * + * { margin-top: 1rem; }
			.space-y-6 > * + * { margin-top: 1.5rem; }
			.gap-4 { gap: 1rem; }
			.gap-6 { gap: 1.5rem; }
			.gap-8 { gap: 2rem; }
			.p-4 { padding: 1rem; }
			.p-6 { padding: 1.5rem; }
			.p-8 { padding: 2rem; }
			.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
			.px-8 { padding-left: 2rem; padding-right: 2rem; }
			.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
			.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
			.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
			.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
			.max-w-3xl { max-width: 48rem; }
			.w-full { width: 100%; }
			.h-full { height: 100%; }
			.min-h-screen { min-height: 100vh; }
			.sticky { position: sticky; }
			.top-0 { top: 0; }
			.z-50 { z-index: 50; }
			.relative { position: relative; }
			.absolute { position: absolute; }
			.transition-colors { transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out; }
			.hover\\:text-blue-600:hover { color: #2563eb; }
			.hover\\:bg-blue-700:hover { background-color: #1d4ed8; }
			.hover\\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }

			/* Grid layouts */
			@media (min-width: 768px) {
				.md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
				.md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
				.md\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
				.md\\:flex { display: flex; }
				.md\\:hidden { display: none; }
				.md\\:text-4xl { font-size: 2.25rem; }
				.md\\:text-5xl { font-size: 3rem; }
				.md\\:text-6xl { font-size: 3.75rem; }
			}

			@media (min-width: 1024px) {
				.lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
				.lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
				.lg\\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
			}

			/* Responsive flex */
			@media (min-width: 640px) {
				.sm\\:flex-row { flex-direction: row; }
			}

			.flex-col { flex-direction: column; }

			/* Links */
			a {
				text-decoration: none;
				color: inherit;
			}

			a:hover {
				text-decoration: none;
			}

			/* Buttons */
			.btn {
				display: inline-block;
				padding: 0.75rem 2rem;
				border-radius: 0.5rem;
				font-weight: 600;
				text-align: center;
				transition: all 0.15s ease-in-out;
				border: none;
				cursor: pointer;
			}

			.btn-primary {
				background-color: #2563eb;
				color: white;
			}

			.btn-primary:hover {
				background-color: #1d4ed8;
			}

			.btn-outline {
				border: 2px solid #2563eb;
				color: #2563eb;
				background-color: transparent;
			}

			.btn-outline:hover {
				background-color: #2563eb;
				color: white;
			}

			/* Gradient background */
			.bg-gradient {
				background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
			}
		</style>
	</head>
	<body class="bg-white text-gray-900">
		<Navigation />
		<slot />
		<Footer />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		min-height: 100%;
		font-family: system-ui, sans-serif;
	}
</style>
