---
import Navigation from '../components/Navigation.astro';
import Footer from '../components/Footer.astro';

export interface Props {
  title: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  structuredData?: object;
}

const {
  title,
  description = "Professional home improvement and carpentry services in Georgia. Licensed contractors providing quality workmanship.",
  keywords = ["home improvement", "carpentry", "contractors", "Georgia"],
  canonicalUrl,
  ogImage,
  structuredData
} = Astro.props;

const currentUrl = new URL(Astro.request.url);
const fullCanonicalUrl = canonicalUrl || currentUrl.href;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />

		<!-- Primary Meta Tags -->
		<title>{title}</title>
		<meta name="title" content={title} />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords.join(', ')} />
		<link rel="canonical" href={fullCanonicalUrl} />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content="website" />
		<meta property="og:url" content={fullCanonicalUrl} />
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		{ogImage && <meta property="og:image" content={ogImage} />}

		<!-- Twitter -->
		<meta property="twitter:card" content="summary_large_image" />
		<meta property="twitter:url" content={fullCanonicalUrl} />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content={description} />
		{ogImage && <meta property="twitter:image" content={ogImage} />}

		<!-- Local Business Schema -->
		{structuredData && (
			<script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
		)}

		<!-- Tailwind CSS -->
		<script src="https://cdn.tailwindcss.com"></script>
	</head>
	<body class="bg-white text-gray-900">
		<Navigation />
		<slot />
		<Footer />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		min-height: 100%;
		font-family: system-ui, sans-serif;
	}
</style>
