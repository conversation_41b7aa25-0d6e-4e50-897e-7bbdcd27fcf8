# Local SEO Website Generator for Service-Based Businesses

This Astro project automatically generates hundreds of SEO-optimized pages for local service businesses by creating unique pages for every service-location combination.

## 🎯 What This Does

For a company like **Deckora Carpentry** with:
- **8 services** (Deck Construction, Bathroom Remodeling, etc.)
- **8 service areas** (Brunswick, St. Simons Island, etc.)

This system automatically generates:
- **64 service-location pages** (e.g., "Deck Construction in Brunswick, GA")
- **8 individual service pages** (e.g., "Deck Construction Services")
- **8 location pages** (e.g., "Services in Brunswick, GA")
- **Navigation and internal linking** between all pages
- **SEO optimization** with meta tags, structured data, and local keywords

## 🚀 Project Structure

Inside of your Astro project, you'll see the following folders and files:

```text
/
├── public/
│   └── favicon.svg
├── src
│   ├── assets
│   │   └── astro.svg
│   ├── components
│   │   └── Welcome.astro
│   ├── layouts
│   │   └── Layout.astro
│   └── pages
│       └── index.astro
└── package.json
```

To learn more about the folder structure of an Astro project, refer to [our guide on project structure](https://docs.astro.build/en/basics/project-structure/).

## 🧞 Commands

All commands are run from the root of the project, from a terminal:

| Command                   | Action                                           |
| :------------------------ | :----------------------------------------------- |
| `npm install`             | Installs dependencies                            |
| `npm run dev`             | Starts local dev server at `localhost:4321`      |
| `npm run build`           | Build your production site to `./dist/`          |
| `npm run preview`         | Preview your build locally, before deploying     |
| `npm run astro ...`       | Run CLI commands like `astro add`, `astro check` |
| `npm run astro -- --help` | Get help using the Astro CLI                     |

## 👀 Want to learn more?

Feel free to check [our documentation](https://docs.astro.build) or jump into our [Discord server](https://astro.build/chat).
